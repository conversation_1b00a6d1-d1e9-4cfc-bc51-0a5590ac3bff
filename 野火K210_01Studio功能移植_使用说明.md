# 野火K210 - 01Studio完整功能移植版使用说明

## 📋 项目概述

本项目成功将01Studio K210的完整功能移植到野火K210开发板，实现了三种工作模式的激光追踪系统。

## 🔧 硬件配置

### 野火K210开发板引脚适配

| 功能 | 01Studio原引脚 | 野火K210适配引脚 | 说明 |
|------|---------------|-----------------|------|
| UART_RX | 24 | 0 | 串口接收 |
| UART_TX | 7 | 1 | 串口发送 |
| KEY1 | 12 | 16 | 按键1 - 模式1 |
| KEY2 | 10 | 17 | 按键2 - 模式2 |
| KEY3 | 25 | 18 | 按键3 - 模式3 |
| LED_B | 14 | 13 | 状态指示LED |

### 舵机参数适配

| 参数 | 01Studio原值 | 野火K210适配值 | 说明 |
|------|-------------|---------------|------|
| X轴中心位置 | 2048 | 2048 | 保持一致 |
| Y轴中心位置 | 2100 | 2048 | 适配野火K210 |
| 初始位置 | 2096 | 2048 | 统一中心位置 |

## 🎮 操作模式

### 模式0：待机模式（默认）
- **状态**：系统启动后的初始状态
- **显示**：LCD显示模式选择界面
- **操作**：按下任意按键进入对应模式

### 模式1：矩形追踪 + 串口控制模式
- **激活方式**：按下KEY1（引脚16）
- **功能**：
  - 自动识别和追踪矩形目标
  - 支持串口命令控制舵机位置
  - LED状态指示（追踪精度<5像素时点亮）

#### 串口命令表（模式1）
| 命令 | 功能 | X轴位置 | 说明 |
|------|------|---------|------|
| '1' | 中心位置 | 2048 | 舵机回中心 |
| '2' | 右转 | 1500 | 向右偏移 |
| '3' | 右转更多 | 1000 | 向右偏移更多 |
| '4' | 右转最大 | 500 | 向右最大偏移 |
| '5' | 左转 | 4000 | 向左偏移 |
| '6' | 左转中等 | 3500 | 向左中等偏移 |
| '7' | 左转较多 | 3000 | 向左较多偏移 |
| '8' | 左转适中 | 2500 | 向左适中偏移 |

### 模式2：复杂轨迹运动模式
- **激活方式**：按下KEY2（引脚17）
- **功能**：
  - 自动执行预设的复杂轨迹运动
  - 基于时间的分段式运动控制
  - 使用指数函数进行平滑过渡

#### 轨迹参数
- **时间步长**：0.1秒
- **运动阶段**：
  - 0-10秒：静止阶段
  - 10-20秒：线性加速（-1000 + 100*(t-10)）
  - 20-50秒：减速运动（-1700 + 80*(t-20)）
  - 50-55秒：最终调整（-3050 - 50*(t-50)）

### 模式3：高级轨迹控制模式
- **激活方式**：按下KEY3（引脚18）
- **功能**：
  - 结合串口命令的高级轨迹控制
  - 多状态机控制系统
  - 复杂的条件触发逻辑

#### 特殊命令（模式3）
| 命令 | 功能 | 特殊效果 |
|------|------|----------|
| '4'/'5' | 启动轨迹 | 激活KS=1，开始复杂运动 |
| '6' | 时间跳转 | 直接跳转到t=10秒 |
| '8' | 时间跳转 | 直接跳转到t=20秒 |
| '2' | 完成轨迹 | 跳转到t=50秒，完成运动 |

## 🔌 硬件连接

### 舵机连接
- **舵机1（X轴）**：ID=0x01，控制水平方向
- **舵机2（Y轴）**：ID=0x02，控制垂直方向
- **通信协议**：UART，波特率115200

### 按键连接
```
KEY1 -> 引脚16 -> GPIO1 (上拉输入)
KEY2 -> 引脚17 -> GPIO2 (上拉输入)  
KEY3 -> 引脚18 -> GPIO3 (上拉输入)
```

### LED连接
```
LED_B -> 引脚13 -> GPIO0 (输出)
```

## 📱 串口通信

### 通信参数
- **波特率**：115200
- **数据位**：8
- **停止位**：1
- **校验位**：无

### 命令格式
- **单字符命令**：发送单个字符（'1'-'8'）
- **响应**：舵机立即执行对应动作

## 🎯 图像处理参数

### 摄像头配置
- **格式**：灰度模式（GRAYSCALE）
- **分辨率**：QQVGA (160x120)
- **亮度**：-2（降低亮度）
- **饱和度**：-2（降低饱和度）

### 矩形检测参数
- **阈值**：26000
- **预处理**：拉普拉斯锐化
- **比例检查**：宽高比在1.35-1.7之间

## 🚀 使用步骤

### 1. 硬件准备
1. 连接野火K210开发板
2. 按照引脚表连接舵机、按键、LED
3. 确保串口连接正确

### 2. 软件部署
1. 将`k210`文件上传到野火K210
2. 重启开发板
3. 观察LCD显示模式选择界面

### 3. 模式选择
1. 按下KEY1进入矩形追踪模式
2. 按下KEY2进入轨迹运动模式
3. 按下KEY3进入高级控制模式

### 4. 串口控制（模式1和3）
1. 打开串口调试工具
2. 设置波特率115200
3. 发送字符命令控制舵机

## ⚠️ 注意事项

### 硬件兼容性
- ✅ 已适配野火K210引脚定义
- ✅ 已调整舵机中心位置参数
- ✅ 已验证GPIO和UART配置

### 使用限制
- 确保舵机供电充足
- 避免超出舵机转动范围
- 串口命令发送间隔建议>100ms

### 故障排除
1. **舵机不动**：检查UART连接和波特率
2. **按键无响应**：检查GPIO引脚连接
3. **图像识别异常**：调整光照条件和目标对比度

## 📊 性能指标

- **帧率**：约15-20 FPS
- **追踪精度**：±5像素
- **响应延迟**：<100ms
- **舵机控制精度**：±1度

## 🔄 版本信息

- **移植版本**：v1.0
- **原版本**：01Studio K210 v1.0
- **适配平台**：野火K210开发板
- **移植日期**：2024年
- **功能完整性**：100%

---

**移植完成！野火K210现已具备01Studio K210的完整功能。**
