# K210完整功能系统 - 基于01Studio功能移植
# 包含矩形追踪、多模式控制、按键操作、LED指示、复杂轨迹运动
# 适配野火K210硬件平台

from maix import GPIO
from machine import UART, Timer, PWM
import time
from math import sqrt, pi, exp
import math
import sensor, image, lcd, utime
from fpioa_manager import fm
import gc
import ujson  # JSON序列化模块

# 硬件初始化 - 完整版功能
def hardware_init():
    """K210硬件初始化 - 包含GPIO按键和LED控制"""
    # 摄像头初始化
    sensor.reset()
    sensor.set_pixformat(sensor.GRAYSCALE)  # 灰度更快
    sensor.set_framesize(sensor.QQVGA)
    sensor.set_brightness(-2)
    sensor.set_saturation(-2)
    sensor.skip_frames(time=2000)

    # LCD初始化
    lcd.init()
    lcd.clear(lcd.WHITE)

    # GPIO初始化 - 适配野火K210引脚
    fm.register(12, fm.fpioa.GPIO1)  # KEY1
    fm.register(10, fm.fpioa.GPIO2)  # KEY2
    fm.register(25, fm.fpioa.GPIO3)  # KEY3
    fm.register(14, fm.fpioa.GPIO0)  # LED

    print("K210硬件初始化完成 - 包含GPIO功能")

# GPIO对象初始化
def init_gpio_objects():
    """初始化GPIO对象"""
    global LED_B, KEY1, KEY2, KEY3
    LED_B = GPIO(GPIO.GPIO0, GPIO.OUT)  # LED对象
    LED_B.value(1)  # 初始熄灭LED
    KEY1 = GPIO(GPIO.GPIO1, GPIO.IN, GPIO.PULL_UP)  # 按键1
    KEY2 = GPIO(GPIO.GPIO2, GPIO.IN, GPIO.PULL_UP)  # 按键2
    KEY3 = GPIO(GPIO.GPIO3, GPIO.IN, GPIO.PULL_UP)  # 按键3
    print("GPIO对象初始化完成")

# 全局变量定义 - 移植01Studio功能
clock = time.clock()
ZX = 80  # 调激光X方向左右偏的地方
xin = 80  # 调激光X方向左右的地方，与ZX同步
k = 0.3
ZY = 70
t = 0
yin = 70
P = 0
P2 = 0
I = 0
I2 = 0
last_cor = 0
PI = 0
PI2 = 0
state = 0
text2 = 0
text2_last = 0
condition = 1
FX = 0
FY = 0
d1c = 0
last_zx = 0
last_zy = 0
black = (150, 255)
cor = [(0, 0), (0, 0), (0, 0), (0, 0)]
TM = 0  # 工作模式标志
AB = 0
AC = 0
CD = 0
DB = 0
state2 = 0
state3 = 0
state4 = 0
state5 = 0
KS = 0

# 位置数据持久化存储模块
def save_position_to_file(position_data):
    """保存位置数据到文件
    Args:
        position_data (dict): 位置数据字典
    Returns:
        bool: 保存成功返回True，失败返回False
    """
    try:
        # 主文件路径
        main_file = "/flash/servo_position.json"
        backup_file = "/flash/servo_position_backup.json"

        # 保存到主文件
        with open(main_file, 'w') as f:
            ujson.dump(position_data, f)

        # 创建备份文件
        with open(backup_file, 'w') as f:
            ujson.dump(position_data, f)

        print("位置数据保存成功")
        return True

    except Exception as e:
        print("保存位置数据失败:", e)
        return False

def load_position_from_file():
    """从文件加载位置数据
    Returns:
        dict or None: 成功返回位置数据，失败返回None
    """
    files_to_try = ["/flash/servo_position.json", "/flash/servo_position_backup.json"]

    for filepath in files_to_try:
        try:
            with open(filepath, 'r') as f:
                data = ujson.load(f)
                if validate_position_data(data):
                    print("位置数据加载成功:", filepath)
                    return data
                else:
                    print("位置数据格式无效:", filepath)
        except Exception as e:
            print("加载位置数据失败:", filepath, e)
            continue

    print("无法加载位置数据，使用默认值")
    return None

def validate_position_data(data):
    """验证位置数据格式
    Args:
        data (dict): 待验证的数据
    Returns:
        bool: 数据有效返回True，无效返回False
    """
    if not isinstance(data, dict):
        return False

    required_fields = ['x', 'y', 'timestamp', 'locked']
    for field in required_fields:
        if field not in data:
            return False

    # 验证位置范围
    if not (1000 <= data['x'] <= 3000) or not (1000 <= data['y'] <= 3000):
        return False

    return True

def reset_to_saved_position():
    """复位舵机到保存的位置
    Returns:
        bool: 复位成功返回True，失败返回False
    """
    position_data = load_position_from_file()
    if not position_data:
        print("无保存位置数据，复位失败")
        return False

    target_x = position_data['x']
    target_y = position_data['y']

    print("开始复位到保存位置: X=%d, Y=%d" % (target_x, target_y))

    # 增强的多步骤复位策略
    reset_steps = [
        {"interval": 2000, "desc": "慢速移动"},
        {"interval": 1000, "desc": "中速确认"},
        {"interval": 500, "desc": "快速微调"}
    ]

    for step_idx, step in enumerate(reset_steps):
        print("复位步骤 %d/3: %s" % (step_idx + 1, step["desc"]))

        # 每个步骤重试3次
        for attempt in range(3):
            try:
                func_servo(0x01, target_x, step["interval"])  # X轴舵机
                func_servo(0x02, target_y, step["interval"])  # Y轴舵机
                time.sleep(step["interval"] / 1000.0 + 0.1)  # 等待舵机移动完成
                print("  尝试 %d/3 成功" % (attempt + 1))
                break  # 成功则跳出重试循环
            except Exception as e:
                print("  尝试 %d/3 失败: %s" % (attempt + 1, e))
                if attempt == 2:  # 最后一次尝试失败
                    print("复位步骤 %d 失败，继续下一步骤" % (step_idx + 1))
                time.sleep(0.1)

    print("舵机复位完成")
    return True

def auto_reset_on_startup():
    """启动时自动复位到保存位置（如果存在）
    Returns:
        bool: 复位成功或无需复位返回True
    """
    position_data = load_position_from_file()
    if not position_data:
        print("无保存位置，跳过启动复位")
        return True

    if position_data.get('locked', False):
        print("检测到锁定位置，执行启动复位...")
        return reset_to_saved_position()
    else:
        print("位置未锁定，跳过启动复位")
        return True

def manual_reset_command():
    """手动复位命令接口
    Returns:
        bool: 复位成功返回True
    """
    print("执行手动复位命令...")
    success = reset_to_saved_position()
    if success:
        print("手动复位成功")
    else:
        print("手动复位失败")
    return success

# 用户交互接口模块
def check_serial_command():
    """非阻塞检查串口命令
    Returns:
        str or None: 返回命令字符串，无命令返回None
    """
    try:
        if uart.any():
            command = uart.read().decode('utf-8').strip().upper()
            return command
    except:
        pass
    return None

def handle_command(command, position_manager_state):
    """处理串口命令
    Args:
        command (str): 命令字符串
        position_manager_state (dict): 位置管理状态
    Returns:
        str: 命令执行结果
    """
    global servo_x_pos, servo_y_pos

    if command == "LOCK":
        # 手动锁定当前位置
        if not position_manager_state['is_locked']:
            saved_position = {
                "x": servo_x_pos,
                "y": servo_y_pos,
                "timestamp": time.time(),
                "locked": True,
                "version": "1.0"
            }

            if save_position_to_file(saved_position):
                position_manager_state['is_locked'] = True
                position_manager_state['saved_position'] = saved_position
                result = "LOCK OK: X=%d Y=%d" % (servo_x_pos, servo_y_pos)
            else:
                result = "LOCK FAILED: Save error"
        else:
            result = "ALREADY LOCKED"

    elif command == "UNLOCK":
        # 解锁恢复追踪模式
        position_manager_state['is_locked'] = False
        position_manager_state['first_detection'] = True
        result = "UNLOCK OK: Tracking resumed"

    elif command == "RESET":
        # 复位到保存位置
        if manual_reset_command():
            result = "RESET OK: Position restored"
        else:
            result = "RESET FAILED: No saved position"

    elif command == "STATUS":
        # 查询系统状态
        if position_manager_state['is_locked']:
            if position_manager_state['saved_position']:
                result = "STATUS: LOCKED X=%d Y=%d" % (
                    position_manager_state['saved_position']['x'],
                    position_manager_state['saved_position']['y']
                )
            else:
                result = "STATUS: LOCKED (no position)"
        else:
            result = "STATUS: TRACKING X=%d Y=%d" % (servo_x_pos, servo_y_pos)

    else:
        result = "UNKNOWN COMMAND: %s" % command

    print("Command: %s -> %s" % (command, result))
    return result

def display_enhanced_status(img, position_manager_state, fps):
    """增强的状态显示
    Args:
        img: 图像对象
        position_manager_state (dict): 位置管理状态
        fps (float): 帧率
    """
    # 显示FPS
    img.draw_string(0, 120, "FPS: %2.1f" % fps, color=(255, 0, 0), scale=1)

    # 显示系统状态
    if position_manager_state['is_locked']:
        img.draw_string(0, 140, "Mode: LOCKED", color=(255, 0, 0), scale=1)
        if position_manager_state['saved_position']:
            img.draw_string(0, 155, "Saved: X%d Y%d" % (
                position_manager_state['saved_position']['x'],
                position_manager_state['saved_position']['y']
            ), color=(255, 255, 0), scale=1)
    else:
        img.draw_string(0, 140, "Mode: TRACKING", color=(0, 255, 0), scale=1)

    # 显示命令提示
    img.draw_string(0, 170, "Cmd: LOCK/UNLOCK/RESET/STATUS", color=(128, 128, 128), scale=1)

# 舵机控制函数
def func_servo(id0, posit0, interval0):
    """舵机控制函数"""
    ZT1 = 0xFF
    ZT2 = 0xFF
    DATA1 = 0X2A
    DATA2 = (posit0 >> 8) & 0xff
    DATA3 = posit0 & 0xff
    DATA4 = (interval0 >> 8) & 0xff
    DATA5 = interval0 & 0xff
    data_length = 0x07
    WriteDATA = 0X03
    GetChecksum = (~(id0 + data_length + WriteDATA + DATA1 + DATA2 + DATA3 + DATA4 + DATA5)) & 0xff
    text = bytes([ZT1, ZT2, id0, data_length, WriteDATA, DATA1, DATA2, DATA3, DATA4, DATA5, GetChecksum])
    uart.write(text)

def main():
    """主程序 - 01Studio完整功能移植版"""
    global uart, LED_B, KEY1, KEY2, KEY3, TM, text2, text2_last, state
    global ZX, ZY, xin, yin, P, P2, I, I2, PI, PI2, cor, last_cor
    global positx, posit, interval, ID1, ID2, theta, t, k, KS
    global AB, AC, CD, DB, state2, state3, state4, state5, d1c

    # 硬件初始化
    hardware_init()

    # 初始化GPIO对象
    init_gpio_objects()

    # 01Studio参数初始化
    intiposit = 2096
    posit = 2100  # 调激光Y方向上下的地方，改小是往下偏，改大往上偏
    positx = 2048
    interval = 1000
    ID1 = 0x01
    ID2 = 0x02
    theta = 0

    # 串口初始化 - 适配野火K210引脚
    fm.register(0, fm.fpioa.UART1_RX, force=True)
    fm.register(1, fm.fpioa.UART1_TX, force=True)
    uart = UART(UART.UART1, 115200, read_buf_len=4096)

    # 舵机初始化
    func_servo(ID1, int(positx), interval)
    func_servo(ID2, int(posit), interval)
    time.sleep(1)

    print("01Studio完整功能系统启动...")

    # 主循环 - 01Studio完整功能
    while True:
        img = sensor.snapshot()

        # 按键检测和模式切换
        if TM == 0 and KEY1.value() == 0:  # 按键被按下接地
            TM = 1
        if TM == 0 and KEY2.value() == 0:  # 按键被按下接地
            TM = 2
        if TM == 0 and KEY3.value() == 0:  # 按键被按下接地
            TM = 3

        # 模式1：矩形追踪 + 串口控制
        if TM == 1:
            text = uart.read(1)  # 读取数据
            if text:
                text2 = text.decode('utf-8')

            # 图像预处理
            img.laplacian(1, sharpen=True)

            for r in img.find_rects(threshold=26000):
                img.draw_rectangle(r.rect(), color=(255, 0, 0))
                for p in r.corners():
                    img.draw_circle(p[0], p[1], 5, color=(0, 255, 0))

                cor = r.corners()

                if last_cor != 0 and ((cor[2][0] - cor[3][0]) / (cor[1][1] - cor[3][1]) > 1.7 or
                                     (cor[2][0] - cor[3][0]) / (cor[1][1] - cor[3][1]) < 1.35):
                    cor = last_cor
                last_cor = cor
                print(cor)

            ZX = (cor[0][0] + cor[1][0] + cor[2][0] + cor[3][0]) / 4
            ZY = (cor[0][1] + cor[1][1] + cor[2][1] + cor[3][1]) / 4

            P = (ZX - xin) * 0
            P2 = (ZY - yin) * 0
            I = (ZX - xin) * 0.5 + I
            I2 = (ZY - yin) * 0.5 + I2
            PI = P + I
            PI2 = P2 + I2

            if abs(ZX - xin) < 5:
                LED_B.value(0)  # 点亮LED
            else:
                LED_B.value(1)  # 熄灭LED

            # 串口命令处理
            theta = theta + 0.001
            if theta >= 2 * pi:
                theta = 0

            r = pi * 40
            if text2 != text2_last:
                state = 1
            text2_last = text2

            if state == 1:
                if text2 == '1':
                    positx = 2048
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0
                elif text2 == '2':
                    positx = 1500  # 往右转
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0
                elif text2 == '3':
                    positx = 1000
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0
                elif text2 == '4':
                    positx = 500
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0
                elif text2 == '5':
                    positx = 4000  # 往左转
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0
                elif text2 == '6':
                    positx = 3500  # 往左转
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0
                elif text2 == '7':
                    positx = 3000  # 往左转
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0
                elif text2 == '8':
                    positx = 2500  # 往左转
                    PI = 0
                    PI2 = 0
                    func_servo(ID1, int(positx - PI), interval)
                    func_servo(ID2, int(posit - PI2), interval)
                    time.sleep(1)
                    state = 0

            func_servo(ID1, int(positx - PI), interval)
            func_servo(ID2, int(posit), interval)

            lcd.display(img)
        # 模式2：复杂轨迹运动模式
        elif TM == 2:
            t0 = 0.1
            t = t + t0
            print(positx)
            print(t)

            k = 0.15
            if KS == 1:
                if state3 == 0:
                    state3 = 1
                    t = 0
                if t > 0:
                    PI = 0
                if t > 10:
                    PI = -1000 + 100 * (t - 10)
                if t > 20:
                    PI = -1700 + 80 * (t - 20)
                    state4 = 1
                if t > 50:
                    PI = -3050 - 50 * (t - 50)
                    if t > 55:
                        t = 55

            func_servo(ID1, int(positx + PI), interval)
            func_servo(ID2, int(posit), interval)
            lcd.display(img)

        # 模式3：高级轨迹控制模式
        elif TM == 3:
            text = uart.read(1)  # 读取数据
            if text:
                text2 = text.decode('utf-8')

            LED_B.value(0)  # 点亮LED

            # 复杂轨迹控制逻辑
            if (text2 == '5' and d1c == 0) or (text2 == '4' and d1c == 0) or (text2 == '4' and d1c == 2):
                AB = 1
                AC = 0
                CD = 0
                DB = 0
                if d1c == 0:
                    positx = 3850
                    d1c = 1
                    KS = 1
                    AB = 0
                    state = 0
                if d1c == 2:
                    state3 = 0

            if text2 == '6':
                PI = 0
                PI2 = 0
                AB = 0
                t = 10
                CD = 0
                DB = 0
                state = 0

            if text2 == '8':
                PI = 0
                PI2 = 0
                AB = 0
                t = 20
                CD = 0
                DB = 0
                state = 0

            if text2 == '2' and state4 == 1:
                PI = 0
                PI2 = 0
                AB = 0
                t = 50
                CD = 0
                DB = 0
                state = 0
                d1c = 2

            t0 = 0.1
            t = t + t0
            print(positx)
            print(t)

            k = 0.15
            if KS == 1:
                if state3 == 0:
                    state3 = 1
                    t = 0
                if t > 0:
                    PI = 0
                if t > 10:
                    PI = -1000 + 100 * (t - 10)
                if t > 20:
                    PI = -1700 + 80 * (t - 20)
                    state4 = 1
                if t > 50:
                    PI = -3050 - 50 * (t - 50)
                    if t > 55:
                        t = 55

            func_servo(ID1, int(positx + PI), interval)
            func_servo(ID2, int(posit), interval)
            lcd.display(img)

        # 默认模式：显示模式选择界面
        else:
            img.draw_string(10, 50, "Mode Selection:", color=(255, 255, 255), scale=2)
            img.draw_string(10, 80, "KEY1: Tracking Mode", color=(0, 255, 0), scale=1)
            img.draw_string(10, 100, "KEY2: Trajectory Mode", color=(0, 255, 0), scale=1)
            img.draw_string(10, 120, "KEY3: Advanced Mode", color=(0, 255, 0), scale=1)
            img.draw_string(10, 150, "Current: TM=%d" % TM, color=(255, 255, 0), scale=1)
            lcd.display(img)

            # 计算矩形中心 - 完全对应原代码
            corners = r.corners()
            center_x = (corners[0][0] + corners[1][0] + corners[2][0] + corners[3][0]) / 4
            center_y = (corners[0][1] + corners[1][1] + corners[2][1] + corners[3][1]) / 4

            # 绘制中心点
            img.draw_circle(int(center_x), int(center_y), 8, color=(0, 0, 255))

            # 首次检测锁定逻辑
            if first_detection and not is_locked:
                # 记录当前舵机位置
                saved_position = {
                    "x": servo_x_pos,
                    "y": servo_y_pos,
                    "timestamp": time.time(),
                    "locked": True,
                    "version": "1.0"
                }

                # 保存位置数据
                if save_position_to_file(saved_position):
                    is_locked = True
                    first_detection = False
                    # 同步状态字典
                    position_manager_state['is_locked'] = is_locked
                    position_manager_state['first_detection'] = first_detection
                    position_manager_state['saved_position'] = saved_position
                    print("首次检测锁定成功! 位置: X=%d, Y=%d" % (servo_x_pos, servo_y_pos))
                else:
                    print("位置保存失败，继续追踪模式")

            # PID控制计算 - 只在未锁定状态下执行
            if not is_locked:
                # PID控制计算 - 完全按照原代码逻辑
                error_x = center_x - target_x  # 对应原代码 (ZX - xin)
                error_y = center_y - target_y  # 对应原代码 (ZY - yin)

                # 积分项更新 - 对应原代码 I = (ZX - xin) * 1 + I
                integral_x = error_x * ki_x + integral_x  # 完全对应原代码
                integral_y = error_y * ki_y + integral_y  # 完全对应原代码

                # PID输出 - 对应原代码 PI = P + I
                P_x = error_x * kp_x  # 对应原代码 P = (ZX - xin) * 0
                P_y = error_y * kp_y  # 对应原代码 P2 = (ZY - yin) * 0
                output_x = P_x + integral_x  # 对应原代码 PI = P + I
                output_y = P_y + integral_y  # 对应原代码 PI2 = P2 + I2

                # 舵机位置计算 - 对应原代码逻辑
                servo_x_pos = servo_x_center - int(output_x)  # 对应原代码 positx - PI
                servo_y_pos = servo_y_center + int(output_y)  # 对应原代码 posit + PI2

                # 舵机位置限制
                servo_x_pos = max(1000, min(3000, servo_x_pos))
                servo_y_pos = max(1000, min(3000, servo_y_pos))

                # 舵机控制
                func_servo(ID1, servo_x_pos, interval)
                func_servo(ID2, servo_y_pos, interval)

                # 显示追踪信息
                img.draw_string(0, 0, "Tracking: ON", color=(0, 255, 0), scale=2)
                img.draw_string(0, 20, "X:%d Y:%d" % (int(center_x), int(center_y)), color=(255, 255, 255), scale=1)
            else:
                # 锁定状态显示
                img.draw_string(0, 0, "LOCKED", color=(255, 0, 0), scale=2)
                if saved_position:
                    img.draw_string(0, 20, "Saved: X:%d Y:%d" % (saved_position['x'], saved_position['y']), color=(255, 255, 0), scale=1)
                img.draw_string(0, 40, "Detected: X:%d Y:%d" % (int(center_x), int(center_y)), color=(255, 255, 255), scale=1)

        # 无矩形检测时的状态显示
        if not rectangles:
            if is_locked:
                img.draw_string(0, 0, "LOCKED - No Target", color=(255, 165, 0), scale=2)
                if saved_position:
                    img.draw_string(0, 20, "Saved: X:%d Y:%d" % (saved_position['x'], saved_position['y']), color=(255, 255, 0), scale=1)
            else:
                img.draw_string(0, 0, "Searching...", color=(0, 255, 255), scale=2)
        
        # 绘制屏幕中心十字线
        img.draw_cross(target_x, target_y, color=(255, 255, 0), scale=4)
        
        # 显示FPS
        fps = clock.fps()
        img.draw_string(0, 100, "FPS: %2.1f" % fps, color=(255, 0, 0), scale=1)
        
        # LCD显示
        lcd.display(img)

# 程序入口
if __name__ == "__main__":
    main()
