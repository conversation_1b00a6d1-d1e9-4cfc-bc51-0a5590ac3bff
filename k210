from maix import GPIO
from fpioa_manager import fm
from board import board_info

from machine import Timer,PWM
import time
from math import sqrt, pi,exp
import math

from machine import UART
from fpioa_manager import fm
import lcd,image,utime
import sensor

sensor.reset(dual_buff=True)
sensor.set_pixformat(sensor.GRAYSCALE) # 灰度更快sensor.RGB565   sensor.GRAYSCAL
sensor.set_framesize(sensor.QVGA)  # 野火K210标准分辨率320x240
sensor.set_brightness(-2)
sensor.set_saturation(-2)
sensor.skip_frames(time = 1000)  # 野火K210标准延迟

lcd.init() #初始化LCD
lcd.clear(lcd.WHITE) #清屏白色

# 野火K210标准GPIO配置
fm.register(board_info.USER_LED, fm.fpioa.GPIOHS0, force=True)
LED_B = GPIO(GPIO.GPIOHS0, GPIO.OUT) #构建LED对象
LED_B.value(1) #熄灭LED

# 野火K210按键配置 - 使用标准BOOT_KEY作为多功能按键
fm.register(board_info.BOOT_KEY, fm.fpioa.GPIOHS1, force=True)
KEY1 = GPIO(GPIO.GPIOHS1, GPIO.IN, GPIO.PULL_NONE)
# 注意：野火K210只有一个标准按键，我们用它来循环切换模式
# 模拟KEY2和KEY3（实际使用时通过串口命令或单按键多次按压实现）
KEY2 = KEY1  # 共用同一个按键
KEY3 = KEY1  # 共用同一个按键

clock = time.clock()
ZX=80###########调激光X方向左右偏的地方这个改的都是最终跟踪到的目标位置
xin=80###########调激光X方向左右的地方，这个改的都是最终跟踪到的目标位置  这两个数字要同步改！
k=0.3
ZY=70
t=0
yin=70
P=0
P2=0
I=0
I2=0
last_cor=0
PI=0
PI2=0
state=0
text2=0
text2_last=0
condition=1
FX=0
FY=0
d1c=0
last_zx=0
last_zy=0
black=(150,255)
cor=[(0,0),(0,0),(0,0),(0,0)]
TM=0
AB=0
AC=0
CD=0
DB=0
state2=0
state3=0
state4=0
state5=0
KS=0
key_pressed = False  # 按键状态标志
last_key_time = 0    # 上次按键时间
if condition:
    def func_servo(id0,posit0,interval0):
            ZT1=0xFF
            ZT2=0xFF
            DATA1=0X2A
            DATA2=(posit0>>8)&0xff
            DATA3=posit0&0xff
            DATA4=(interval0>>8)&0xff
            DATA5=interval0&0xff  # 条件满足时执行的代码
            data_length=0x07
            WriteDATA=0X03
            GetChecksum=(~(id0+data_length+WriteDATA+DATA1+DATA2+DATA3+DATA4+DATA5))& 0xff
            text=bytes([ZT1,ZT2,id0,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
            uart.write(text) #数据回传

    intiposit=2048  # 野火K210舵机初始位置
    posit=2048      # 野火K210 Y轴舵机中心位置
    positx=2048     # 野火K210 X轴舵机中心位置
    interval=1000
    ID1=0x01
    ID2=0x02
    theta=0

    #映射串口引脚 - 适配野火K210硬件
    fm.register(0, fm.fpioa.UART1_RX, force=True)
    fm.register(1, fm.fpioa.UART1_TX, force=True)

    #初始化串口
    uart = UART(UART.UART1, 115200, read_buf_len=4096)
    #uart.write('Hello 01Studio!')
    func_servo(ID1,int(positx),interval)
    func_servo(ID2,int(posit),interval)
    time.sleep(1)
    while True:
        img = sensor.snapshot()
        # 野火K210单按键模式切换逻辑
        current_time = utime.ticks_ms()
        if KEY1.value() == 0:  # 按键被按下
            if not key_pressed and (current_time - last_key_time > 300):  # 防抖动
                if TM == 0:
                    TM = 1  # 进入模式1
                elif TM == 1:
                    TM = 2  # 切换到模式2
                elif TM == 2:
                    TM = 3  # 切换到模式3
                elif TM == 3:
                    TM = 0  # 回到待机模式
                key_pressed = True
                last_key_time = current_time
                print("模式切换到:", TM)
        else:
            key_pressed = False
        #print(TM)
        if TM==1 :
            text=uart.read(1) #读取数据
            if text:

                 text2=text.decode('utf-8')

                 #print(text2) #REPL打印
        # 下面的`threshold`应设置为足够高的值，以滤除在图像中检测到的具有
        # 低边缘幅度的噪声矩形。最适用与背景形成鲜明对比的矩形。
            img.laplacian(1, sharpen=True)

            for r in img.find_rects(threshold = 26000):
                img.draw_rectangle(r.rect(), color = (255, 0, 0))
                for p in r.corners(): img.draw_circle(p[0], p[1], 5, color = (0, 255, 0))

                cor=r.corners()

                if last_cor!=0 and ((cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])>1.7 or (cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])<1.35) :
                    cor=last_cor
                #if last_cor!=0 and ((abs(last_cor[0][0]-cor[0][0])>80) or   (abs(last_cor[0][1]-cor[0][1])>50)):
                    #cor=last_cor
                last_cor=cor
                print(cor)

                #print(r.corners())
            ZX=(cor[0][0]+ cor[1][0]+ cor[2][0]+  cor[3][0] )/4
            ZY=(cor[0][1]+ cor[1][1]+ cor[2][1]+  cor[3][1] )/4


            P=(ZX-xin )*0
            P2=(ZY-yin)*0
            I=(ZX-xin )*0.5+I
            I2=(ZY-yin)*0.5+I2
            PI=P+I
            PI2=P2+I2
            if abs(ZX-xin)<5 :
                LED_B.value(0) #点亮LED+
            else :
                LED_B.value(1) #点亮LED
            #print(P)
        #print("FPS %f" % clock.fps())
            #text=uart.read() #读取数据
            #print(text.decode('hex'))

            theta=theta+0.001
            if theta>=2*pi:
                theta=0

            r=pi*40
            if text2 != text2_last:
                state=1
            text2_last=text2
            if state==1:
                if text2=='1':
                    positx=2048
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0

                if text2=='2':
                    positx=1500#往右转
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0
                if text2=='3':
                    positx=1000
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0
                if text2=='4':
                    positx=500
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0
                if text2=='5':
                    positx=4000#往左转
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0
                if text2=='6':
                    positx=3500#往左转
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0
                if text2=='7':
                    positx=3000#往左转
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0
                if text2=='8':
                    positx=2500#往左转
                    PI=0
                    PI2=0
                    func_servo(ID1,int(positx-PI),interval)
                    func_servo(ID2,int(posit-PI2),interval)
                    time.sleep(1)
                    state=0

            #print(positx)
            func_servo(ID1,int(positx-PI),interval)
            func_servo(ID2,int(posit),interval)
            #print(x)
            #print(y)
            #text=bytes([ZT1,ZT2,ID1,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
            #uart.write(text) #数据回传


            lcd.display(img)
#############################################################################################################
#############################################################################################################
#############################################################################################################
        #if TM==2 :
                #text=uart.read(1) #读取数据
                #if text:

                     #text2=text.decode('utf-8')

                     ##print(text2) #REPL打印
            ## 下面的`threshold`应设置为足够高的值，以滤除在图像中检测到的具有
            ## 低边缘幅度的噪声矩形。最适用与背景形成鲜明对比的矩形。
                #img.laplacian(1, sharpen=True)

                #for r in img.find_rects(threshold = 26000):
                    #img.draw_rectangle(r.rect(), color = (255, 0, 0))
                    #for p in r.corners(): img.draw_circle(p[0], p[1], 5, color = (0, 255, 0))

                    #cor=r.corners()

                    #if last_cor!=0 and ((cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])>1.7 or (cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])<1.35) :
                        #cor=last_cor
                    ##if last_cor!=0 and ((abs(last_cor[0][0]-cor[0][0])>80) or   (abs(last_cor[0][1]-cor[0][1])>50)):
                        ##cor=last_cor
                    #last_cor=cor
                    #print(cor)

                    ##print(r.corners())
                #ZX=(cor[0][0]+ cor[1][0]+ cor[2][0]+  cor[3][0] )/4
                #ZY=(cor[0][1]+ cor[1][1]+ cor[2][1]+  cor[3][1] )/4


                #P=(ZX-xin )*0
                #P2=(ZY-yin)*0
                #I=(ZX-xin )*1+I
                #I2=(ZY-yin)*1+I2
                #PI=P+I
                #PI2=P2+I2
                #if abs(ZX-xin)<5 :
                    #LED_B.value(0) #点亮LED+
                #else :
                    #LED_B.value(1) #点亮LED
                ##print(P)
            ##print("FPS %f" % clock.fps())
                ##text=uart.read() #读取数据
                ##print(text.decode('hex'))

                #theta=theta+0.001
                #if theta>=2*pi:
                    #theta=0

                #r=pi*40
                #if text2 != text2_last:
                    #state=1
                #text2_last=text2
                #if state==1:
                    #if text2=='1':
                        #positx=2048
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0

                    #if text2=='2':
                        #positx=1500#往右转
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0
                    #if text2=='3':
                        #positx=1000
                        #if d1c==0 :
                            #positx=3700
                            #d1c=1
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0
                    #if text2=='4':
                        #positx=3700
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0
                    #if text2=='5':
                        #positx=3700#往左转
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0
                    #if text2=='6':
                        #positx=3400#往左转
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0
                    #if text2=='7':
                        #positx=3000#往左转
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0
                    #if text2=='8':
                        #positx=2500#往左转
                        ##PI=0
                        ##PI2=0
                        #func_servo(ID1,int(positx-PI),interval)
                        #func_servo(ID2,int(posit-PI2),interval)

                        #state=0

                ##print(positx)
                #func_servo(ID1,int(positx-PI),interval)
                #func_servo(ID2,int(posit),interval)
                ##print(x)
                ##print(y)
                ##text=bytes([ZT1,ZT2,ID1,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
                ##uart.write(text) #数据回传


                #lcd.display(img)
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
        if TM==2 :
            text=uart.read(1) #读取数据
            if text:

                 text2=text.decode('utf-8')

                 #print(text2) #REPL打印
        # 下面的`threshold`应设置为足够高的值，以滤除在图像中检测到的具有
        # 低边缘幅度的噪声矩形。最适用与背景形成鲜明对比的矩形。
            #img.laplacian(1, sharpen=True)

            #for r in img.find_rects(threshold = 26000):
                #img.draw_rectangle(r.rect(), color = (255, 0, 0))
                #for p in r.corners(): img.draw_circle(p[0], p[1], 5, color = (0, 255, 0))

                #cor=r.corners()

                #if last_cor!=0 and ((cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])>1.7 or (cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])<1.35) :
                    #cor=last_cor
                ##if last_cor!=0 and ((abs(last_cor[0][0]-cor[0][0])>80) or   (abs(last_cor[0][1]-cor[0][1])>50)):
                    ##cor=last_cor
                #last_cor=cor
                #print(cor)

                #print(r.corners())
            #ZX=(cor[0][0]+ cor[1][0]+ cor[2][0]+  cor[3][0] )/4
            #ZY=(cor[0][1]+ cor[1][1]+ cor[2][1]+  cor[3][1] )/4


            #P=(ZX-xin )*0
            #P2=(ZY-yin)*0
            #I=(ZX-xin )*0+I
            #I2=(ZY-yin)*0+I2
            #PI=P+I
            #PI2=P2+I2
            LED_B.value(0) #点亮LED+
            #if abs(ZX-xin)<5 :
                #LED_B.value(0) #点亮LED+
            #else :
                #LED_B.value(1) #点亮LED
            #print(P)
        #print("FPS %f" % clock.fps())
            #text=uart.read() #读取数据
            #print(text.decode('hex'))

            theta=theta+0.001
            if theta>=2*pi:
                theta=0

            r=pi*40
            if text2 != text2_last:
                state2=1
            text2_last=text2
            if state2==1:
                if text2=='1':
                    AB=0
                    AC=0
                    CD=0
                    DB=0
                    state2=0

                if text2=='2':
                    AB=1
                    AC=0
                    CD=0
                    DB=0
                    state2=0
                if text2=='3':
                    AB=0
                    AC=1
                    CD=0
                    DB=0
                    state2=0
                if text2=='4':
                    AB=0
                    AC=0
                    CD=1
                    DB=0
                    state2=0
                if text2=='5':
                    AB=0
                    AC=0
                    CD=0
                    DB=1
                    state2=0
                if text2=='6':
                    AB=1
                    AC=1
                    CD=0
                    DB=0
                    state2=0
                if text2=='7':
                    AB=0
                    AC=1
                    CD=1
                    DB=0
                    state2=0
                if text2=='8':
                    AB=0
                    AC=0
                    CD=1
                    DB=1
                    state2=0

            #print(positx)
            if AB==1:
                FX=r*math.cos(theta)+xin
                FY=r*math.sin(theta)+yin
            if AC==1:
                FX=r*math.cos(theta+pi/2)+xin
                FY=r*math.sin(theta+pi/2)+yin
            if CD==1:
                FX=r*math.cos(theta+pi)+xin
                FY=r*math.sin(theta+pi)+yin
            if DB==1:
                FX=r*math.cos(theta+3*pi/2)+xin
                FY=r*math.sin(theta+3*pi/2)+yin
            if AB==1 and AC==1:
                FX=r*math.cos(theta+pi/4)+xin
                FY=r*math.sin(theta+pi/4)+yin
            if AC==1 and CD==1:
                FX=r*math.cos(theta+3*pi/4)+xin
                FY=r*math.sin(theta+3*pi/4)+yin
            if CD==1 and DB==1:
                FX=r*math.cos(theta+5*pi/4)+xin
                FY=r*math.sin(theta+5*pi/4)+yin
            if DB==1 and AB==1:
                FX=r*math.cos(theta+7*pi/4)+xin
                FY=r*math.sin(theta+7*pi/4)+yin
            if AB==0 and AC==0 and CD==0 and DB==0:
                FX=xin
                FY=yin

            P=(FX-xin )*0.5
            P2=(FY-yin)*0.5
            I=(FX-xin )*0.5+I
            I2=(FY-yin)*0.5+I2
            PI=P+I
            PI2=P2+I2
            func_servo(ID1,int(positx-PI),interval)
            func_servo(ID2,int(posit-PI2),interval)
            #print(x)
            #print(y)
            #text=bytes([ZT1,ZT2,ID1,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
            #uart.write(text) #数据回传


            lcd.display(img)
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
        if TM==3 :
            text=uart.read(1) #读取数据
            if text:

                 text2=text.decode('utf-8')

                 #print(text2) #REPL打印
        # 下面的`threshold`应设置为足够高的值，以滤除在图像中检测到的具有
        # 低边缘幅度的噪声矩形。最适用与背景形成鲜明对比的矩形。
            #img.laplacian(1, sharpen=True)

            #for r in img.find_rects(threshold = 26000):
                #img.draw_rectangle(r.rect(), color = (255, 0, 0))
                #for p in r.corners(): img.draw_circle(p[0], p[1], 5, color = (0, 255, 0))

                #cor=r.corners()

                #if last_cor!=0 and ((cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])>1.7 or (cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])<1.35) :
                    #cor=last_cor
                ##if last_cor!=0 and ((abs(last_cor[0][0]-cor[0][0])>80) or   (abs(last_cor[0][1]-cor[0][1])>50)):
                    ##cor=last_cor
                #last_cor=cor
                #print(cor)

                #print(r.corners())
            #ZX=(cor[0][0]+ cor[1][0]+ cor[2][0]+  cor[3][0] )/4
            #ZY=(cor[0][1]+ cor[1][1]+ cor[2][1]+  cor[3][1] )/4


            #P=(ZX-xin )*0
            #P2=(ZY-yin)*0
            #I=(ZX-xin )*0+I
            #I2=(ZY-yin)*0+I2
            #PI=P+I
            #PI2=P2+I2
            LED_B.value(0) #点亮LED+
            #if abs(ZX-xin)<5 :
                #LED_B.value(0) #点亮LED+
            #else :
                #LED_B.value(1) #点亮LED
            #print(P)
        #print("FPS %f" % clock.fps())
            #text=uart.read() #读取数据
            #print(text.decode('hex'))

            theta=theta+0.001
            if theta>=2*pi:
                theta=0

            r=pi*40
            if text2 != text2_last:
                state4=1
            text2_last=text2
            if state4==1:
                if text2=='1':
                    positx=2048
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    KS=0
                    state4=0

                if text2=='2':
                    positx=1500#往右转
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    KS=1
                    state4=0
                if text2=='3':
                    positx=1000
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    KS=0
                    state4=0
                if text2=='4':
                    positx=500
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    KS=0
                    state4=0
                if text2=='5':
                    positx=4000#往左转
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    KS=0
                    state4=0
                #if text2=='6':
                    #positx=3400#往左转
                    #PI=0
                    #PI2=0
                    #func_servo(ID1,int(positx),interval)
                    #func_servo(ID2,int(posit),interval)
                    #AB=0
                    #AC=0
                    #CD=1
                    #DB=0
                    #state=0

                #if text2=='7':
                    #positx=3200#往左转
                    ##PI=0
                    ##PI2=0
                    #func_servo(ID1,int(positx),interval)
                    #func_servo(ID2,int(posit),interval)
                    #AB=0
                    #AC=0
                    #CD=1
                    #DB=0
                    #state=0

                #if text2=='8':
                    #positx=2048
                    ##PI=0
                    ##PI2=0
                    #func_servo(ID1,int(positx),interval)
                    #func_servo(ID2,int(posit),interval)
                    #AB=0
                    #AC=0
                    #CD=0
                    #DB=1
                    #state=0
            t0=0.1
            t=t+t0
            print(positx)
            print(t)


            k=0.15
            if KS==1:
                if state3==0 :
                    state3=1
                    t=0
                if t>0:
                    PI=0#-150*(1-exp(-k*t))/(1-exp(-5*k))
                if t>10 :

                    PI=-1000+100*(t-10)
                if t>20 :
                    PI=-1700+80*(t-20)
                    state4=1
                if t>50 :

                    PI=-3050-50*(t-50)#-150*(1-exp(-k*t))/(1-exp(-5*k))
                    if t > 55 :
                        t=55
            #if CD==1:
                #if state3==0:
                    #t=0
                    #state3=1

                #PI=-(500*k*exp(-k*t))/(1-exp(-5*k))
            #if DB==1:
                #if state4==0:
                    #t=0
                    #state4=1
                #PI=-(500*k*exp(-k*t))/(1-exp(-5*k))
            #if AB==1:
                #if state5==0:
                    #t=0
                    #state5=1
                #PI=-(300*k*exp(-k*t))/(1-exp(-5*k))
            func_servo(ID1,int(positx+PI),interval)
            func_servo(ID2,int(posit),interval)
            ##print(x)
            #print(y)
            #text=bytes([ZT1,ZT2,ID1,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
            #uart.write(text) #数据回传


            #lcd.display(img)
##############################################################################################################
##############################################################################################################
##############################################################################################################
##############################################################################################################
##############################################################################################################
##############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
#############################################################################################################
        if TM==3 :
            text=uart.read(1) #读取数据
            if text:

                 text2=text.decode('utf-8')

                 #print(text2) #REPL打印
        # 下面的`threshold`应设置为足够高的值，以滤除在图像中检测到的具有
        # 低边缘幅度的噪声矩形。最适用与背景形成鲜明对比的矩形。
            #img.laplacian(1, sharpen=True)

            #for r in img.find_rects(threshold = 26000):
                #img.draw_rectangle(r.rect(), color = (255, 0, 0))
                #for p in r.corners(): img.draw_circle(p[0], p[1], 5, color = (0, 255, 0))

                #cor=r.corners()

                #if last_cor!=0 and ((cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])>1.7 or (cor[2][0]-cor[3][0])/(cor[1][1]-cor[3][1])<1.35) :
                    #cor=last_cor
                ##if last_cor!=0 and ((abs(last_cor[0][0]-cor[0][0])>80) or   (abs(last_cor[0][1]-cor[0][1])>50)):
                    ##cor=last_cor
                #last_cor=cor
                #print(cor)

                #print(r.corners())
            #ZX=(cor[0][0]+ cor[1][0]+ cor[2][0]+  cor[3][0] )/4
            #ZY=(cor[0][1]+ cor[1][1]+ cor[2][1]+  cor[3][1] )/4


            #P=(ZX-xin )*0
            #P2=(ZY-yin)*0
            #I=(ZX-xin )*0+I
            #I2=(ZY-yin)*0+I2
            #PI=P+I
            #PI2=P2+I2
            LED_B.value(0) #点亮LED+
            #if abs(ZX-xin)<5 :
                #LED_B.value(0) #点亮LED+
            #else :
                #LED_B.value(1) #点亮LED
            #print(P)
        #print("FPS %f" % clock.fps())
            #text=uart.read() #读取数据
            #print(text.decode('hex'))

            theta=theta+0.001
            if theta>=2*pi:
                theta=0

            r=pi*40
            if text2 != text2_last:
                state5=1
            text2_last=text2
            if state5==1:
                if text2=='1':
                    positx=2048
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=0
                    AC=0
                    CD=0
                    DB=0
                    state5=0

                if text2=='2':
                    positx=1500#往右转
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=1
                    AC=0
                    CD=0
                    DB=0
                    state5=0
                if text2=='3':
                    positx=1000
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=0
                    AC=1
                    CD=0
                    DB=0
                    state5=0
                if text2=='4':
                    positx=500
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=0
                    AC=0
                    CD=1
                    DB=0
                    state5=0
                if text2=='5':
                    positx=4000#往左转
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=0
                    AC=0
                    CD=0
                    DB=1
                    state5=0
                if text2=='6':
                    positx=3400#往左转
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=0
                    AC=0
                    CD=1
                    DB=0
                    state5=0
                if text2=='7':
                    positx=3200#往左转
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=0
                    AC=0
                    CD=1
                    DB=0
                    state5=0

                if text2=='8':
                    positx=2048
                    #PI=0
                    #PI2=0
                    func_servo(ID1,int(positx),interval)
                    func_servo(ID2,int(posit),interval)
                    AB=0
                    AC=0
                    CD=0
                    DB=1
                    state5=0
            t0=0.1
            t=t+t0
            print(positx)
            print(t)


            k=0.15
            if KS==1:
                if state3==0 :
                    state3=1
                    t=0
                if t>0:
                    PI=0#-150*(1-exp(-k*t))/(1-exp(-5*k))
                if t>10 :

                    PI=-1000+100*(t-10)
                if t>20 :
                    PI=-1700+80*(t-20)
                    state4=1
                if t>50 :

                    PI=-3050-50*(t-50)#-150*(1-exp(-k*t))/(1-exp(-5*k))
                    if t > 55 :
                        t=55
            #if CD==1:
                #if state3==0:
                    #t=0
                    #state3=1

                #PI=-(500*k*exp(-k*t))/(1-exp(-5*k))
            #if DB==1:
                #if state4==0:
                    #t=0
                    #state4=1
                #PI=-(500*k*exp(-k*t))/(1-exp(-5*k))
            #if AB==1:
                #if state5==0:
                    #t=0
                    #state5=1
                #PI=-(300*k*exp(-k*t))/(1-exp(-5*k))
            func_servo(ID1,int(positx+PI),interval)
            func_servo(ID2,int(posit),interval)
            ##print(x)
            #print(y)
            #text=bytes([ZT1,ZT2,ID1,data_length,WriteDATA,DATA1,DATA2,DATA3,DATA4,DATA5,GetChecksum])
            #uart.write(text) #数据回传


            #lcd.display(img)
##############################################################################################################
##############################################################################################################
##############################################################################################################
##############################################################################################################
##############################################################################################################
##############################################################################################################



#else:
    #def func_servo(servo,angle):
        #servo.duty((angle+90)/180*10+2.5)

    #tim = Timer(Timer.TIMER0, Timer.CHANNEL0, mode=Timer.MODE_PWM)
    #S1 = PWM(tim, freq=50, duty=0, pin=8)
    #tim2 = Timer(Timer.TIMER0, Timer.CHANNEL1, mode=Timer.MODE_PWM)
    #S2 = PWM(tim2, freq=50, duty=0, pin=9)
    #theta=0
    #intheta=0
    #func_servo(S1,0)
    #func_servo(S2,0)
    #time.sleep(3)
    #while True:

        #theta=theta+0.0001
        #if theta>=2*pi:
            #theta=0

        #r=pi*3
        #x=r*math.cos(theta)
        #y=r*math.sin(theta)
        #func_servo(S1,int(intheta+x))
        #func_servo(S2,int(intheta+y))
        #print(x)
        #print(y)





